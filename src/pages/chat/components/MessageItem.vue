<script setup lang="ts">
import { computed } from 'vue'
import { useChatStore } from '@/store/chatStore'
import MarkdownRenderer from './MarkdownRenderer.vue'
import ChartContainer from './charts/ChartContainer.vue'
import MessageToolbar from './MessageToolbar.vue'
import { openDocumentPreview, getFileIcon } from '@/utils/documentPreview.js'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  isStreaming?: boolean
  docs?: any[]
  chart_data?: string
}

interface Props {
  message: Message
  currentKnowledgeName?: string
}

const props = defineProps<Props>()
const chatStore = useChatStore()

// 判断是否处于思考状态（已创建AI消息但还未开始流式输出）
const isThinking = computed(() => {
  return props.message.type === 'assistant' &&
         props.message.isStreaming &&
         (!chatStore.streaming.active || chatStore.streaming.messageId !== props.message.id)
})

// 判断是否正在流式输出
const isStreamingOutput = computed(() => {
  return props.message.type === 'assistant' &&
         props.message.isStreaming &&
         chatStore.streaming.active &&
         chatStore.streaming.messageId === props.message.id
})

// 处理文档数据
const processedDocs = computed(() => {
  if (!props.message.docs?.length) return []

  const docMap = new Map()

  props.message.docs.forEach(docStr => {
    try {
      const doc = JSON.parse(docStr)
      const isPdf = doc.filename?.toLowerCase().endsWith('.pdf')
      const fileName = doc.filename

      if (docMap.has(fileName)) {
        if (isPdf && doc.page) {
          const existing = docMap.get(fileName)
          if (!existing.pages.includes(doc.page)) {
            existing.pages.push(doc.page)
          }
        }
      } else {
        docMap.set(fileName, {
          fileName,
          link: doc.url,
          page: doc.page,
          fileType: isPdf ? 'pdf' : 'word',
          pages: isPdf && doc.page ? [doc.page] : []
        })
      }
    } catch (error) {
      console.error('解析文档失败:', error)
    }
  })

  return Array.from(docMap.values())
})

// 处理图表点击事件
const handleChartClick = (data: any) => {
  console.log('图表点击事件:', data)
  // 可以在这里添加图表点击的处理逻辑
}

// 处理文档预览
const handleDocumentPreview = (doc: any) => {
  if (!doc.link) {
    uni.showModal({
      title: '提示',
      content: '文档链接不存在，这可能是一个示例文档。是否查看文档预览功能演示？',
      success: (res) => {
        if (res.confirm) {
          // 使用一个示例PDF链接进行演示
          openDocumentPreview({
            fileUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
            fileName: '示例文档.pdf',
            fileType: 'pdf'
          })
        }
      }
    })
    return
  }

  openDocumentPreview({
    fileUrl: doc.link,
    fileName: doc.fileName,
    fileType: doc.fileType
  })
}

// 处理消息删除
const handleMessageDelete = (messageId: string) => {
  chatStore.deleteMessage(messageId)
}

// 处理消息点赞
const handleMessageLike = (data: { message: any; liked: boolean }) => {
  // 更新消息的点赞状态
  const message = chatStore.messages.find(msg => msg.id === data.message.id)
  if (message) {
    message.liked = data.liked
  }
}
</script>

<template>
  <view 
    class="message-item"
    :class="{ 'user-message': message.type === 'user', 'ai-message': message.type === 'assistant' }"
  >
    <view class="message-content">
      <!-- 头像 -->
      <view class="avatar" :class="message.type === 'user' ? 'avatar-user' : 'avatar-ai'">
        <text class="avatar-text">{{ message.type === 'user' ? '我' : 'AI' }}</text>
      </view>

      <!-- 消息气泡 -->
      <view class="message-bubble">
        <!-- 用户消息显示纯文本 -->
        <text v-if="message.type === 'user'" class="message-text">{{ message.content }}</text>

        <!-- AI消息处理 -->
        <template v-else>
          <!-- 思考状态 -->
          <view v-if="isThinking" class="thinking-indicator">
            <text class="thinking-text">AI正在思考中...</text>
            <view class="thinking-animation">
              <view class="thinking-dot"></view>
              <view class="thinking-dot"></view>
              <view class="thinking-dot"></view>
            </view>
          </view>

          <!-- 正常内容渲染 -->
          <MarkdownRenderer
            v-else
            :content="message.content"
            :is-streaming="message.isStreaming"
          />

          <!-- 流式输出动画 -->
          <view v-if="isStreamingOutput" class="typing-indicator">
            <text class="typing-dot">●</text>
            <text class="typing-dot">●</text>
            <text class="typing-dot">●</text>
          </view>
        </template>



        <!-- 图表数据 -->
        <ChartContainer
          v-if="message.type === 'assistant' && message.chart_data"
          :chart-data="message.chart_data"
          width="100%"
          height="380px"
          @chart-click="handleChartClick"
          class="message-chart"
        />

        <!-- 文档来源 -->
        <view v-if="message.type === 'assistant' && processedDocs.length > 0" class="docs-section">
          <view class="section-title">参考文件</view>
          <view class="docs-list">
            <view
              v-for="(doc, index) in processedDocs"
              :key="index"
              class="doc-item"
              @click="handleDocumentPreview(doc)"
            >
              <view class="doc-info">
                <view class="doc-header">
                  <text class="doc-icon">{{ getFileIcon(doc.fileName) }}</text>
                  <text class="doc-name">{{ doc.fileName }}</text>
                  <text class="preview-icon">👁️</text>
                </view>
                <view v-if="doc.fileType === 'pdf' && doc.pages.length > 0" class="pages-info">
                  <text class="pages-label">页码：</text>
                  <text v-for="(page, pageIndex) in doc.pages" :key="pageIndex" class="page-number">{{ page }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 消息操作工具栏 - 只对AI消息显示 -->
        <MessageToolbar
          v-if="message.type === 'assistant'"
          :message="message"
          @delete="handleMessageDelete"
          @like="handleMessageLike"
        />

      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.message-item {
  margin-bottom: $spacing-xl;

  &.user-message .message-content {
    flex-direction: row-reverse;

    .message-bubble {
      background-color: $accent-color;
      color: #FFFFFF;
      margin-left: 80rpx;
    }
  }

  &.ai-message .message-content {
    flex-direction: row;

    .message-bubble {
      background-color: $chat-bubble-bot-bg;
      color: $text-primary;
      border: 1rpx solid $border-color;
      margin-right: 80rpx;
    }
  }
}

.message-content {
  display: flex;
  gap: $spacing-lg;
  align-items: flex-start;
}

.avatar {
  width: $avatar-size;
  height: $avatar-size;
  border-radius: $border-radius-circle;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 500;

  &.avatar-user {
    background: linear-gradient(135deg, $accent-color 0%, #0d47d9 100%);
    color: #FFFFFF;
  }

  &.avatar-ai {
    background: linear-gradient(135deg, #10B981 0%, #06B6D4 50%, #3B82F6 100%);
    color: #FFFFFF;
  }
}

.message-bubble {
  max-width: calc(100% - 144rpx);
  padding: $spacing-lg $spacing-xl;
  border-radius: $border-radius-sm;
  position: relative;
  word-wrap: break-word;
}

.message-text {
  font-size: 30rpx;
  line-height: 1.6;
}

// Markdown 渲染器样式调整
:deep(.markdown-renderer) {
  .markdown-content {
    .github-markdown-body {
      font-size: 30rpx !important;
    }
  }

  .streaming-indicator {
    margin-top: 8rpx;
  }
}



// 文档来源样式
.docs-section {
  margin-top: $spacing-lg;
  padding: $spacing-lg;
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: $border-radius-sm;
  border-left: 4rpx solid $accent-color;
}

.section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: $accent-color;
  margin-bottom: $spacing-sm;
}

.docs-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.doc-item {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.doc-name {
  font-size: 28rpx;
  color: $text-primary;
  font-weight: 500;
}

.pages-info {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.pages-label {
  font-size: 24rpx;
  color: $text-secondary;
}

.page-number {
  font-size: 24rpx;
  color: $accent-color;
  background-color: rgba(20, 91, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-right: $spacing-xs;
}

// 消息中的图表样式
.message-chart {
  width: calc(100vw - 80rpx); // 占满屏幕宽度，减去左右边距
  max-width: 100%;
  margin: $spacing-lg 0; // 上下边距，左右为0
  position: relative;
  left: 50%;
  transform: translateX(-50%); // 水平居中

  // 确保在小屏幕上也有合适的显示
  @media (max-width: 750rpx) {
    width: calc(100vw - 60rpx);
  }
}

// 思考状态样式
.thinking-indicator {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-lg 0;
  color: $text-secondary;
}

.thinking-text {
  font-size: 28rpx;
  color: $text-secondary;
}

.thinking-animation {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.thinking-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: $text-secondary;
  animation: thinking 1.4s infinite;

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

@keyframes thinking {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 流式输出动画样式
.typing-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: $spacing-sm;
}

.typing-dot {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  animation: typing 1.4s infinite;

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 0.3;
  }
  30% {
    opacity: 1;
  }
}

// 文档来源样式
.docs-section {
  margin-top: $spacing-lg;
  padding: $spacing-lg;
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: $border-radius-sm;
  border-left: 4rpx solid $accent-color;
}

.section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: $accent-color;
  margin-bottom: $spacing-sm;
}

.docs-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.doc-item {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  padding: $spacing-sm $spacing-md;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: $border-radius-sm;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.4);
  }

  &:active {
    transform: scale(0.98);
  }
}

.doc-info {
  width: 100%;
}

.doc-header {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.doc-icon {
  font-size: 28rpx;
  flex-shrink: 0;
}

.doc-name {
  font-size: 26rpx;
  color: $text-primary;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.preview-icon {
  font-size: 24rpx;
  color: $accent-color;
  flex-shrink: 0;
  opacity: 0.7;
}

.pages-info {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.pages-label {
  font-size: 24rpx;
  color: $text-secondary;
}

.page-number {
  font-size: 24rpx;
  color: $accent-color;
  background-color: rgba(20, 91, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-right: $spacing-xs;
}
</style>
