<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Props {
  isSending: boolean
  safeAreaBottom: number
}

defineProps<Props>()

const emit = defineEmits<{
  send: [text: string]
}>()

const inputText = ref('')
const isRecording = ref(false)
const recordingText = ref('')

// 语音识别相关
let recognition: any = null
let isRecognitionSupported = false

// 初始化语音识别
const initSpeechRecognition = () => {
  if (typeof window !== 'undefined') {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    if (SpeechRecognition) {
      recognition = new SpeechRecognition()
      recognition.continuous = false
      recognition.interimResults = true
      recognition.lang = 'zh-CN'

      recognition.onstart = () => {
        isRecording.value = true
        recordingText.value = '正在录音...'
      }

      recognition.onresult = (event: any) => {
        let finalTranscript = ''
        let interimTranscript = ''

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript
          if (event.results[i].isFinal) {
            finalTranscript += transcript
          } else {
            interimTranscript += transcript
          }
        }

        if (finalTranscript) {
          inputText.value = finalTranscript
          recordingText.value = ''
        } else if (interimTranscript) {
          recordingText.value = `识别中: ${interimTranscript}`
        }
      }

      recognition.onend = () => {
        isRecording.value = false
        recordingText.value = ''
      }

      recognition.onerror = (event: any) => {
        console.error('语音识别错误:', event.error)
        isRecording.value = false
        recordingText.value = ''

        let errorMessage = '语音识别失败'
        switch (event.error) {
          case 'no-speech':
            errorMessage = '没有检测到语音，请重试'
            break
          case 'audio-capture':
            errorMessage = '无法访问麦克风'
            break
          case 'not-allowed':
            errorMessage = '麦克风权限被拒绝'
            break
          case 'network':
            errorMessage = '网络错误，请检查网络连接'
            break
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none'
        })
      }

      isRecognitionSupported = true
    }
  }
}

// 开始录音
const startRecording = () => {
  if (!isRecognitionSupported) {
    uni.showToast({
      title: '当前浏览器不支持语音识别',
      icon: 'none'
    })
    return
  }

  if (isRecording.value) {
    stopRecording()
    return
  }

  try {
    recognition.start()
  } catch (error) {
    console.error('启动语音识别失败:', error)
    uni.showToast({
      title: '启动语音识别失败',
      icon: 'none'
    })
  }
}

// 停止录音
const stopRecording = () => {
  if (recognition && isRecording.value) {
    recognition.stop()
  }
}

const sendMessage = () => {
  if (!inputText.value.trim()) return

  const text = inputText.value.trim()
  inputText.value = ''
  emit('send', text)
}

onMounted(() => {
  initSpeechRecognition()
})

onUnmounted(() => {
  if (recognition) {
    recognition.stop()
  }
})
</script>

<template>
  <view class="input-area">
    <view class="input-wrapper">
      <view class="input-container">
        <textarea
          v-model="inputText"
          class="input-field"
          :placeholder="isRecording ? recordingText : '输入您的问题...'"
          :disabled="isSending"
          auto-height
          :maxlength="1000"
          @confirm="sendMessage"
        />
      </view>
      <view class="action-buttons">
        <!-- 语音录制按钮 -->
        <button
          class="voice-button"
          :class="{ 'recording': isRecording, 'disabled': isSending }"
          @click="startRecording"
          :disabled="isSending"
        >
          <text class="voice-icon">{{ isRecording ? '⏸️' : '🎤' }}</text>
        </button>

        <!-- 发送按钮 -->
        <button
          class="send-button"
          :class="{ 'sending': isSending, 'disabled': !inputText.trim() }"
          @click="sendMessage"
          :disabled="isSending || !inputText.trim()"
        >
          <text class="send-icon">{{ isSending ? '⏸' : '→' }}</text>
        </button>
      </view>
    </view>
    <!-- 安全区域占位 -->
    <view class="safe-area" :style="{ height: safeAreaBottom + 'px' }"></view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-top: 1rpx solid #E5E7EB;
  padding: 20rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 20rpx;
}

.input-container {
  flex: 1;
  background-color: #F8F9FA;
  border: 1rpx solid #E5E7EB;
  border-radius: 24rpx;
  padding: 24rpx;
  transition: all 0.2s ease;
  min-height: 80rpx;
  display: flex;
  align-items: center;

  &:focus-within {
    border-color: #145BFF;
    box-shadow: 0 0 0 2rpx rgba(20, 91, 255, 0.1);
  }
}

.input-field {
  width: 100%;
  min-height: 40rpx;
  max-height: 200rpx;
  font-size: 32rpx;
  color: #1F2937;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  line-height: 1.4;

  &::placeholder {
    color: #9CA3AF;
    font-size: 30rpx;
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.send-button {
  width: 80rpx;
  height: 80rpx;
  background-color: #145BFF;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:not(.disabled):active {
    background-color: #0d47d9;
    transform: scale(0.95);
  }

  &.disabled {
    background-color: #D1D5DB;
    opacity: 0.6;
  }

  &.sending {
    background-color: #FF4757;
  }
}

.voice-button {
  width: 80rpx;
  height: 80rpx;
  background-color: #10B981;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:not(.disabled):active {
    background-color: #059669;
    transform: scale(0.95);
  }

  &.disabled {
    background-color: #D1D5DB;
    opacity: 0.6;
  }

  &.recording {
    background-color: #EF4444;
    animation: pulse 1.5s infinite;
  }
}

.voice-icon {
  width: 28rpx;
  height: 28rpx;
  color: #FFFFFF;
  font-size: 28rpx;
}

.send-icon {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: bold;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.safe-area {
  background-color: #FFFFFF;
  min-height: 20rpx; /* 最小安全区域高度 */
}
</style>
